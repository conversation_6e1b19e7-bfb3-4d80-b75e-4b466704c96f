import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { ScrollView } from 'react-native';

import { CloseAccountSection } from '../../components/(tabs)/settings/CloseAccountSection';
import { HeaderSection } from '../../components/(tabs)/settings/HeaderSection';
import { SignOutButton } from '../../components/(tabs)/settings/SignOutButton';
import { UserProfileCard } from '../../components/(tabs)/settings/UserProfileCard';
import { useSettingsScreenData } from '../../hooks/composite/useSettingsScreenData';

export default function Settings() {
  const { user, deletingAccount, error, handleSignOut, handleDeleteAccount } = useSettingsScreenData();

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='flex-1 px-6 pb-8'>
          <VStack space='xl' className='flex-1'>
            <UserProfileCard user={user} />
            <SignOutButton onSignOut={handleSignOut} />
            <CloseAccountSection
              onCloseAccount={handleDeleteAccount}
              isDeleting={deletingAccount}
              error={error}
            />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
