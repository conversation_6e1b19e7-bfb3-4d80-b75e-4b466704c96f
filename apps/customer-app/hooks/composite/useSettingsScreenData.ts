import { useAuth } from '@indie-points/contexts';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useCallback } from 'react';
import { Alert } from 'react-native';

export function useSettingsScreenData() {
  const { user, signOut, loading } = useAuth();

  const handleSignOut = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Sign out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign out',
        style: 'destructive',
        onPress: async () => {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  }, [signOut]);

  return {
    user,
    loading,
    handleSignOut,
  };
}
