import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Button, ButtonText } from '@gluestack-ui/button';
import { HStack } from '@gluestack-ui/hstack';
import { Spinner } from '@gluestack-ui/spinner';
import React from 'react';

interface CloseAccountButtonProps {
  onCloseAccount: () => void;
  isDeleting: boolean;
}

export function CloseAccountButton({ onCloseAccount, isDeleting }: CloseAccountButtonProps) {
  return (
    <Button
      size='lg'
      onPress={onCloseAccount}
      isDisabled={isDeleting}
      bg='$red600'
      borderColor='$red800'
      borderWidth={2}
      borderRadius={12}
      shadowColor='$red800'
      shadowOffset={{ width: 0, height: 2 }}
      shadowOpacity={0.25}
      shadowRadius={4}
      $pressed={{
        bg: '$red700',
      }}
    >
      <HStack alignItems='center' justifyContent='center'>
        {isDeleting ? (
          <Spinner size='small' color='white' />
        ) : (
          <FontAwesome
            name='trash'
            size={20}
            color='white'
            style={{ marginRight: 8 }}
          />
        )}
        <ButtonText color='white' fontWeight='$semibold' fontSize='$lg' ml={8}>
          {isDeleting ? 'Closing Account...' : 'Close Account'}
        </ButtonText>
      </HStack>
    </Button>
  );
}
