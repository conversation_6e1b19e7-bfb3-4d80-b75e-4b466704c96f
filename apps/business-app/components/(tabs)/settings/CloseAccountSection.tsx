import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@gluestack-ui/box';
import { HStack } from '@gluestack-ui/hstack';
import { Text } from '@gluestack-ui/text';
import { VStack } from '@gluestack-ui/vstack';
import React from 'react';

import { CloseAccountButton } from './CloseAccountButton';

interface CloseAccountSectionProps {
  onCloseAccount: () => void;
  isDeleting: boolean;
  error: string | null;
}

export function CloseAccountSection({ onCloseAccount, isDeleting, error }: CloseAccountSectionProps) {
  return (
    <Box
      bg='$red50'
      borderColor='$red200'
      borderWidth={2}
      borderRadius={12}
      p={24}
    >
      <VStack space='lg'>
        {/* Header */}
        <HStack alignItems='center'>
          <FontAwesome name='warning' size={24} color='#dc2626' />
          <Text color='$red700' fontWeight='$bold' fontSize='$xl' ml={12}>
            Close Account
          </Text>
        </HStack>

        {/* Warning Text */}
        <VStack space='md'>
          <Text color='$red700' fontWeight='$medium' fontSize='$md'>
            ⚠️ This action cannot be undone
          </Text>
          
          <Text color='$red600' fontSize='$sm' lineHeight={20}>
            Closing your account will permanently delete:
          </Text>
          
          <VStack space='xs' ml={16}>
            <Text color='$red600' fontSize='$sm'>• Your business profile and settings</Text>
            <Text color='$red600' fontSize='$sm'>• All created rewards and campaigns</Text>
            <Text color='$red600' fontSize='$sm'>• Customer transaction history</Text>
            <Text color='$red600' fontSize='$sm'>• Your login credentials</Text>
          </VStack>
          
          <Text color='$red700' fontWeight='$medium' fontSize='$sm' mt={8}>
            You will lose access to all customer data and analytics.
          </Text>
        </VStack>

        {/* Error Message */}
        {error && (
          <Box
            bg='$red100'
            borderColor='$red300'
            borderWidth={1}
            borderRadius={8}
            p={12}
          >
            <Text color='$red700' fontSize='$sm' fontWeight='$medium'>
              {error}
            </Text>
          </Box>
        )}

        {/* Close Account Button */}
        <CloseAccountButton 
          onCloseAccount={onCloseAccount}
          isDeleting={isDeleting}
        />
      </VStack>
    </Box>
  );
}
